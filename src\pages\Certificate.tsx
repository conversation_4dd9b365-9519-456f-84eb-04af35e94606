import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Printer, RefreshCw } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

const Certificate = () => {
  const [userName, setUserName] = useState('');
  const [userPlace, setUserPlace] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('Cybersecurity Professional Course');
  const [isGenerating, setIsGenerating] = useState(false);
  const certificateRef = useRef<HTMLDivElement>(null);

  const courseOptions = [
    'Cybersecurity Professional Course',
    'Ethical Hacking & Penetration Testing',
    'Web Application Security',
    'Network Security Fundamentals',
    'Digital Forensics & Incident Response',
    'Malware Analysis & Reverse Engineering',
    'Cloud Security Essentials',
    'Linux Security Administration'
  ];

  const handleGenerateCertificate = () => {
    const cleanedName = userName.trim().replace(/\s+/g, ' ');
    const cleanedPlace = userPlace.trim().replace(/\s+/g, ' ');

    if (!cleanedName || !cleanedPlace) {
      toast({
        title: "Missing Information",
        description: "Please enter both name and place to generate certificate.",
        variant: "destructive",
      });
      return;
    }

    if (cleanedName.length < 2) {
      toast({
        title: "Invalid Name",
        description: "Please enter a valid name with at least 2 characters.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    // Simulate generation process
    setTimeout(() => {
      setIsGenerating(false);
      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been successfully generated.",
      });
    }, 1500);
  };

  const handleDownload = () => {
    const cleanedName = userName.trim().replace(/\s+/g, ' ');
    const cleanedPlace = userPlace.trim().replace(/\s+/g, ' ');

    if (!cleanedName || !cleanedPlace) {
      toast({
        title: "Generate Certificate First",
        description: "Please generate the certificate before downloading.",
        variant: "destructive",
      });
      return;
    }

    // Create a canvas to convert SVG to image
    const svgElement = certificateRef.current?.querySelector('svg');
    if (svgElement) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      // Set canvas size to match certificate dimensions
      canvas.width = 842;
      canvas.height = 595;

      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      img.onload = () => {
        // Fill canvas with white background
        if (ctx) {
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        }

        canvas.toBlob((blob) => {
          if (blob) {
            const downloadUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${cleanedName.replace(/\s+/g, '_')}_Certificate.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(downloadUrl);

            toast({
              title: "Download Started",
              description: "Your certificate is being downloaded.",
            });
          }
        }, 'image/png', 1.0);

        URL.revokeObjectURL(url);
      };

      img.onerror = () => {
        toast({
          title: "Download Failed",
          description: "Failed to generate certificate image. Please try again.",
          variant: "destructive",
        });
        URL.revokeObjectURL(url);
      };

      img.src = url;
    }
  };

  const handlePrint = () => {
    const cleanedName = userName.trim().replace(/\s+/g, ' ');
    const cleanedPlace = userPlace.trim().replace(/\s+/g, ' ');

    if (!cleanedName || !cleanedPlace) {
      toast({
        title: "Generate Certificate First",
        description: "Please generate the certificate before printing.",
        variant: "destructive",
      });
      return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow && certificateRef.current) {
      const certificateHTML = certificateRef.current.innerHTML;
      printWindow.document.write(`
        <html>
          <head>
            <title>Certificate - ${cleanedName}</title>
            <style>
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .certificate-container { display: flex; justify-content: center; align-items: center; min-height: 100vh; }
              svg { max-width: 100%; height: auto; }
              @media print {
                body { margin: 0; padding: 0; }
                .certificate-container { min-height: auto; }
              }
            </style>
          </head>
          <body>
            <div class="certificate-container">
              ${certificateHTML}
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleReset = () => {
    setUserName('');
    setUserPlace('');
    setSelectedCourse('Cybersecurity Professional Course');
    toast({
      title: "Form Reset",
      description: "Certificate form has been reset.",
    });
  };

  // Format name input to handle spaces properly
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    // Remove leading spaces and limit consecutive spaces to 1
    value = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
    // Limit to reasonable length
    if (value.length <= 50) {
      setUserName(value);
    }
  };

  // Format place input to handle spaces properly
  const handlePlaceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    // Remove leading spaces and limit consecutive spaces to 1
    value = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
    // Limit to reasonable length
    if (value.length <= 100) {
      setUserPlace(value);
    }
  };

  // Calculate character positions for proper spacing in SVG
  const calculateCharacterPositions = (text: string): string => {
    // Approximate character widths for Garet font at size 65.02 (scaled by transform matrix .7502827)
    const charWidths: { [key: string]: number } = {
      'A': 35, 'B': 34, 'C': 36, 'D': 36, 'E': 30, 'F': 28, 'G': 38,
      'H': 36, 'I': 14, 'J': 24, 'K': 34, 'L': 28, 'M': 44, 'N': 36,
      'O': 39, 'P': 32, 'Q': 39, 'R': 34, 'S': 32, 'T': 32, 'U': 36,
      'V': 34, 'W': 49, 'X': 34, 'Y': 32, 'Z': 32, ' ': 15
    };

    // Calculate total width for centering
    let totalWidth = 0;
    for (let char of text) {
      totalWidth += charWidths[char] || 30;
    }

    // Center the text by adjusting starting position
    const centerOffset = Math.max(0, (200 - totalWidth) / 2); // Approximate centering

    let positions: number[] = [centerOffset];
    let currentX = centerOffset;

    for (let i = 0; i < text.length - 1; i++) {
      const char = text[i];
      const width = charWidths[char] || 30; // Default width
      currentX += width;
      positions.push(currentX);
    }

    return positions.join(' ');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Certificate Generator
          </h1>
          <p className="text-gray-300 text-lg">
            Generate your personalized cybersecurity course completion certificate
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Certificate Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="userName" className="text-gray-300">
                  Full Name
                </Label>
                <Input
                  id="userName"
                  type="text"
                  placeholder="Enter your full name"
                  value={userName}
                  onChange={handleNameChange}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                  maxLength={50}
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>No extra spaces • Uppercase in certificate</span>
                  <span className={userName.length > 40 ? 'text-yellow-400' : ''}>{userName.length}/50</span>
                </div>
                {userName && (
                  <div className="text-xs text-blue-400 mt-1">
                    Preview: {userName.trim().replace(/\s+/g, ' ').toUpperCase()}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="userPlace" className="text-gray-300">
                  Location/Place
                </Label>
                <Input
                  id="userPlace"
                  type="text"
                  placeholder="Enter your location"
                  value={userPlace}
                  onChange={handlePlaceChange}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                  maxLength={100}
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>Organization or location name</span>
                  <span className={userPlace.length > 80 ? 'text-yellow-400' : ''}>{userPlace.length}/100</span>
                </div>
                {userPlace && (
                  <div className="text-xs text-blue-400 mt-1">
                    Preview: {userPlace.trim().replace(/\s+/g, ' ')}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="course" className="text-gray-300">
                  Course
                </Label>
                <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-blue-500">
                    <SelectValue placeholder="Select a course" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    {courseOptions.map((course) => (
                      <SelectItem
                        key={course}
                        value={course}
                        className="text-white hover:bg-gray-600 focus:bg-gray-600"
                      >
                        {course}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleGenerateCertificate}
                  disabled={isGenerating}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate Certificate'
                  )}
                </Button>
                
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Reset
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                
                <Button
                  onClick={handlePrint}
                  variant="secondary"
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Printer className="w-4 h-4 mr-2" />
                  Print
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Certificate Preview */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Certificate Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                ref={certificateRef}
                className="bg-white p-4 rounded-lg shadow-lg overflow-hidden"
              >
                <CertificateTemplate
                  userName={userName}
                  userPlace={userPlace}
                  courseName={selectedCourse}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Certificate Template Component that uses the actual SVG template
const CertificateTemplate: React.FC<{
  userName: string;
  userPlace: string;
  courseName: string;
}> = ({
  userName,
  userPlace,
  courseName
}) => {
  const [svgContent, setSvgContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAndModifySvg = async () => {
      try {
        const response = await fetch('/certificate/certificate_template.svg');
        if (!response.ok) {
          throw new Error('Failed to load certificate template');
        }

        let svgText = await response.text();

        // Add font fallbacks to ensure proper rendering
        svgText = svgText.replace(/font-family="Garet"/g, 'font-family="Garet, Arial, sans-serif"');

        // Replace the default name "ARUN K" with user input
        if (userName && userName.trim()) {
          // Clean the name: remove extra spaces, trim, and format properly
          const cleanedName = userName
            .trim()
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .toUpperCase();

          // Use a simpler approach with centered text for better compatibility
          // Shadow text (with opacity) - centered approach
          svgText = svgText.replace(
            /<text fill="#0d5a62" fill-opacity="\.4" xml:space="preserve" transform="matrix\(\.7502827 0 0 \.7502827 61\.318136 241\.8084\)" font-size="65\.02" font-family="Garet, Arial, sans-serif" font-weight="bold"><tspan y="61" x="[^"]*">ARUN K<\/tspan><\/text>/g,
            `<text fill="#0d5a62" fill-opacity=".4" text-anchor="middle" transform="matrix(.7502827 0 0 .7502827 200 241.8084)" font-size="65.02" font-family="Garet, Arial, sans-serif" font-weight="bold"><tspan y="61" x="0">${cleanedName}</tspan></text>`
          );

          // Main text (white) - centered approach
          svgText = svgText.replace(
            /<text fill="#ffffff" xml:space="preserve" transform="matrix\(\.7502827 0 0 \.7502827 59\.549997 240\.04026\)" font-size="65\.02" font-family="Garet, Arial, sans-serif" font-weight="bold"><tspan y="61" x="[^"]*">ARUN K<\/tspan><\/text>/g,
            `<text fill="#ffffff" text-anchor="middle" transform="matrix(.7502827 0 0 .7502827 200 240.04026)" font-size="65.02" font-family="Garet, Arial, sans-serif" font-weight="bold"><tspan y="61" x="0">${cleanedName}</tspan></text>`
          );
        }

        // Replace course information if needed
        if (courseName) {
          svgText = svgText.replace(/Web Penetration Testing Course/g, courseName);
        }

        // Replace location/organization if needed - be more specific
        if (userPlace && userPlace.trim()) {
          // Replace the organization name in the course description
          svgText = svgText.replace(/organized by Cyber Wolf/g, `organized by ${userPlace}`);
        }

        // Update date to current date with better formatting
        const now = new Date();
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];

        const dayName = dayNames[now.getDay()];
        const day = now.getDate();
        const dayWithSuffix = day + (day % 10 === 1 && day !== 11 ? 'st' :
                                    day % 10 === 2 && day !== 12 ? 'nd' :
                                    day % 10 === 3 && day !== 13 ? 'rd' : 'th');
        const month = monthNames[now.getMonth()];
        const year = now.getFullYear();

        const formattedDate = `${dayName}, ${dayWithSuffix}`;
        const formattedMonthYear = `${month} ${year}`;

        // Replace the date in the SVG
        svgText = svgText.replace(/Friday, 18th,/g, formattedDate);
        svgText = svgText.replace(/July 2025/g, formattedMonthYear);

        setSvgContent(svgText);
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading certificate template:', error);
        toast({
          title: "Error Loading Template",
          description: "Failed to load certificate template. Please try again.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    loadAndModifySvg();
  }, [userName, userPlace, courseName]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <span className="text-gray-600 text-center">
          Loading certificate template...
          <br />
          <span className="text-sm text-gray-500">Preparing fonts and layout</span>
        </span>
      </div>
    );
  }

  if (!svgContent) {
    // Fallback certificate if SVG fails to load
    return (
      <div className="relative w-full bg-white border-2 border-blue-600 rounded-lg p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-blue-800 mb-4">CERTIFICATE OF COMPLETION</h1>
          <p className="text-gray-600 mb-6">This is to certify that</p>
          <h2 className="text-4xl font-bold text-blue-900 mb-2">{userName || 'Your Name'}</h2>
          <div className="w-64 h-0.5 bg-blue-600 mx-auto mb-6"></div>
          <p className="text-gray-700 mb-4">has successfully completed the</p>
          <h3 className="text-2xl font-bold text-blue-800 mb-4">{courseName}</h3>
          <p className="text-gray-700 mb-6">at {userPlace || 'Cyber Wolf Academy'}</p>
          <p className="text-gray-600 mb-8">Date: {new Date().toLocaleDateString()}</p>
          <div className="flex justify-between items-end mt-16">
            <div className="text-center">
              <div className="w-32 h-0.5 bg-gray-400 mb-2"></div>
              <p className="text-sm text-gray-600">Instructor</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-0.5 bg-gray-400 mb-2"></div>
              <p className="text-sm text-gray-600">Director</p>
            </div>
          </div>
          <p className="text-blue-800 font-bold text-xl mt-8">Cyber Wolf Academy</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <div
        dangerouslySetInnerHTML={{ __html: svgContent }}
        className="w-full"
      />
    </div>
  );
};

export default Certificate;
