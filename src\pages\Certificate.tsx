import React, { useState, useRef } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Printer, RefreshCw } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

const Certificate = () => {
  const [userName, setUserName] = useState('');
  const [userPlace, setUserPlace] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('Cybersecurity Professional Course');
  const [isGenerating, setIsGenerating] = useState(false);
  const certificateRef = useRef<HTMLDivElement>(null);

  const courseOptions = [
    'Cybersecurity Professional Course',
    'Ethical Hacking & Penetration Testing',
    'Web Application Security',
    'Network Security Fundamentals',
    'Digital Forensics & Incident Response',
    'Malware Analysis & Reverse Engineering',
    'Cloud Security Essentials',
    'Linux Security Administration'
  ];

  const handleGenerateCertificate = () => {
    if (!userName.trim() || !userPlace.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter both name and place to generate certificate.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    // Simulate generation process
    setTimeout(() => {
      setIsGenerating(false);
      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been successfully generated.",
      });
    }, 1500);
  };

  const handleDownload = () => {
    if (!userName.trim() || !userPlace.trim()) {
      toast({
        title: "Generate Certificate First",
        description: "Please generate the certificate before downloading.",
        variant: "destructive",
      });
      return;
    }

    // Create a canvas to convert SVG to image
    const svgElement = certificateRef.current?.querySelector('svg');
    if (svgElement) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = () => {
        canvas.width = img.width || 842;
        canvas.height = img.height || 595;
        ctx?.drawImage(img, 0, 0);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const downloadUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${userName.replace(/\s+/g, '_')}_Certificate.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(downloadUrl);
          }
        });
        
        URL.revokeObjectURL(url);
      };
      
      img.src = url;
    }
  };

  const handlePrint = () => {
    if (!userName.trim() || !userPlace.trim()) {
      toast({
        title: "Generate Certificate First",
        description: "Please generate the certificate before printing.",
        variant: "destructive",
      });
      return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow && certificateRef.current) {
      const certificateHTML = certificateRef.current.innerHTML;
      printWindow.document.write(`
        <html>
          <head>
            <title>Certificate - ${userName}</title>
            <style>
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .certificate-container { display: flex; justify-content: center; align-items: center; min-height: 100vh; }
              svg { max-width: 100%; height: auto; }
              @media print {
                body { margin: 0; padding: 0; }
                .certificate-container { min-height: auto; }
              }
            </style>
          </head>
          <body>
            <div class="certificate-container">
              ${certificateHTML}
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleReset = () => {
    setUserName('');
    setUserPlace('');
    setSelectedCourse('Cybersecurity Professional Course');
    toast({
      title: "Form Reset",
      description: "Certificate form has been reset.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Certificate Generator
          </h1>
          <p className="text-gray-300 text-lg">
            Generate your personalized cybersecurity course completion certificate
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Certificate Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="userName" className="text-gray-300">
                  Full Name
                </Label>
                <Input
                  id="userName"
                  type="text"
                  placeholder="Enter your full name"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="userPlace" className="text-gray-300">
                  Location/Place
                </Label>
                <Input
                  id="userPlace"
                  type="text"
                  placeholder="Enter your location"
                  value={userPlace}
                  onChange={(e) => setUserPlace(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="course" className="text-gray-300">
                  Course
                </Label>
                <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-blue-500">
                    <SelectValue placeholder="Select a course" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    {courseOptions.map((course) => (
                      <SelectItem
                        key={course}
                        value={course}
                        className="text-white hover:bg-gray-600 focus:bg-gray-600"
                      >
                        {course}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleGenerateCertificate}
                  disabled={isGenerating}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate Certificate'
                  )}
                </Button>
                
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Reset
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                
                <Button
                  onClick={handlePrint}
                  variant="secondary"
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Printer className="w-4 h-4 mr-2" />
                  Print
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Certificate Preview */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Certificate Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                ref={certificateRef}
                className="bg-white p-4 rounded-lg shadow-lg overflow-hidden"
              >
                <CertificateTemplate
                  userName={userName}
                  userPlace={userPlace}
                  courseName={selectedCourse}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Certificate Template Component
const CertificateTemplate: React.FC<{
  userName: string;
  userPlace: string;
  courseName: string;
}> = ({
  userName,
  userPlace,
  courseName
}) => {
  return (
    <div className="relative w-full">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        version="1.1"
        width="842.25"
        height="595.5"
        viewBox="0 0 842.25 595.5"
        className="w-full h-auto"
      >
        {/* Background with gradient */}
        <defs>
          <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: '#f8fafc', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#e2e8f0', stopOpacity: 1 }} />
          </linearGradient>
          <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: '#1e40af', stopOpacity: 1 }} />
            <stop offset="50%" style={{ stopColor: '#3b82f6', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#1e40af', stopOpacity: 1 }} />
          </linearGradient>
        </defs>

        <rect width="842.25" height="595.5" fill="url(#backgroundGradient)" />

        {/* Decorative background pattern */}
        <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="#e2e8f0" opacity="0.3" />
        </pattern>
        <rect width="842.25" height="595.5" fill="url(#dots)" />

        {/* Outer Border */}
        <rect
          x="15"
          y="15"
          width="812.25"
          height="565.5"
          fill="none"
          stroke="url(#borderGradient)"
          strokeWidth="6"
          rx="10"
        />

        {/* Inner Border */}
        <rect
          x="35"
          y="35"
          width="772.25"
          height="525.5"
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          rx="5"
        />

        {/* Decorative corner elements */}
        <g stroke="#3b82f6" strokeWidth="2" fill="none">
          <circle cx="80" cy="80" r="20" />
          <circle cx="762" cy="80" r="20" />
          <circle cx="80" cy="515" r="20" />
          <circle cx="762" cy="515" r="20" />

          {/* Inner corner decorations */}
          <circle cx="80" cy="80" r="10" fill="#3b82f6" opacity="0.2" />
          <circle cx="762" cy="80" r="10" fill="#3b82f6" opacity="0.2" />
          <circle cx="80" cy="515" r="10" fill="#3b82f6" opacity="0.2" />
          <circle cx="762" cy="515" r="10" fill="#3b82f6" opacity="0.2" />
        </g>

        {/* Header */}
        <text
          x="421"
          y="90"
          textAnchor="middle"
          className="fill-blue-800"
          style={{ fontSize: '28px', fontWeight: 'bold', fontFamily: 'Georgia, serif', letterSpacing: '2px' }}
        >
          CERTIFICATE OF COMPLETION
        </text>

        {/* Decorative line under header */}
        <line x1="200" y1="105" x2="642" y2="105" stroke="#3b82f6" strokeWidth="2" />

        {/* Subtitle */}
        <text
          x="421"
          y="140"
          textAnchor="middle"
          className="fill-gray-600"
          style={{ fontSize: '16px', fontFamily: 'Georgia, serif', fontStyle: 'italic' }}
        >
          This is to certify that
        </text>

        {/* Name */}
        <text
          x="421"
          y="200"
          textAnchor="middle"
          className="fill-blue-900"
          style={{ fontSize: '32px', fontWeight: 'bold', fontFamily: 'Georgia, serif', letterSpacing: '1px' }}
        >
          {userName || 'Arun K'}
        </text>

        {/* Decorative underline for name */}
        <line
          x1="150"
          y1="215"
          x2="692"
          y2="215"
          stroke="#1e40af"
          strokeWidth="3"
        />
        <line
          x1="160"
          y1="220"
          x2="682"
          y2="220"
          stroke="#3b82f6"
          strokeWidth="1"
        />

        {/* Course completion text */}
        <text
          x="421"
          y="260"
          textAnchor="middle"
          className="fill-gray-700"
          style={{ fontSize: '16px', fontFamily: 'Georgia, serif' }}
        >
          has successfully completed the comprehensive
        </text>

        {/* Course name */}
        <text
          x="421"
          y="300"
          textAnchor="middle"
          className="fill-blue-800"
          style={{ fontSize: '22px', fontWeight: 'bold', fontFamily: 'Georgia, serif' }}
        >
          {courseName || 'Cybersecurity Professional Course'}
        </text>

        {/* Course details */}
        <text
          x="421"
          y="325"
          textAnchor="middle"
          className="fill-gray-600"
          style={{ fontSize: '14px', fontFamily: 'Georgia, serif', fontStyle: 'italic' }}
        >
          Advanced Penetration Testing & Ethical Hacking
        </text>

        {/* Location text */}
        <text
          x="421"
          y="360"
          textAnchor="middle"
          className="fill-gray-700"
          style={{ fontSize: '16px', fontFamily: 'Georgia, serif' }}
        >
          at {userPlace || 'Cyber Wolf Academy'}
        </text>

        {/* Date */}
        <text
          x="421"
          y="390"
          textAnchor="middle"
          className="fill-gray-600"
          style={{ fontSize: '14px', fontFamily: 'Georgia, serif' }}
        >
          Date of Completion: {new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </text>

        {/* Signature section */}
        <g>
          <text
            x="200"
            y="480"
            textAnchor="middle"
            className="fill-gray-700"
            style={{ fontSize: '12px', fontFamily: 'Georgia, serif' }}
          >
            Instructor Signature
          </text>

          <line
            x1="120"
            y1="490"
            x2="280"
            y2="490"
            stroke="#666"
            strokeWidth="1"
          />

          <text
            x="642"
            y="480"
            textAnchor="middle"
            className="fill-gray-700"
            style={{ fontSize: '12px', fontFamily: 'Georgia, serif' }}
          >
            Director Signature
          </text>

          <line
            x1="562"
            y1="490"
            x2="722"
            y2="490"
            stroke="#666"
            strokeWidth="1"
          />
        </g>

        {/* Organization */}
        <text
          x="421"
          y="530"
          textAnchor="middle"
          className="fill-blue-800"
          style={{ fontSize: '18px', fontWeight: 'bold', fontFamily: 'Georgia, serif' }}
        >
          Cyber Wolf Academy
        </text>

        <text
          x="421"
          y="550"
          textAnchor="middle"
          className="fill-gray-600"
          style={{ fontSize: '12px', fontFamily: 'Georgia, serif' }}
        >
          Professional Cybersecurity Training Institute
        </text>

        {/* Cyber Wolf Logo */}
        <g transform="translate(421, 430)">
          <circle r="18" fill="#1e40af" opacity="0.1" />
          <circle r="15" fill="none" stroke="#1e40af" strokeWidth="2" />
          <text
            textAnchor="middle"
            dy="5"
            className="fill-blue-800"
            style={{ fontSize: '14px', fontWeight: 'bold', fontFamily: 'Arial, sans-serif' }}
          >
            CW
          </text>
        </g>

        {/* Certificate ID */}
        <text
          x="750"
          y="580"
          textAnchor="end"
          className="fill-gray-400"
          style={{ fontSize: '10px', fontFamily: 'monospace' }}
        >
          Certificate ID: CW-{new Date().getFullYear()}-{Math.random().toString(36).substr(2, 6).toUpperCase()}
        </text>
      </svg>
    </div>
  );
};

export default Certificate;
